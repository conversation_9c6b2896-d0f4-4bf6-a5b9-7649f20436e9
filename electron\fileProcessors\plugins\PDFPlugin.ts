/**
 * PDF Processing Plugin
 * Optional plugin for processing PDF files using pdf-parse
 */

import * as fs from 'fs'
import * as path from 'path'
import { FileProcessorPlugin, ProcessedFileContent } from '../types'

export default class PDFPlugin implements FileProcessorPlugin {
  name = 'PDFPlugin'
  version = '1.0.0'
  description = 'Optional plugin for processing PDF files'
  author = 'ChatLo Team'
  dependencies = ['pdf-parse']
  optional = true

  supportedTypes = ['pdf']
  supportedExtensions = ['.pdf']

  private pdfParse: any = null

  async initialize(): Promise<void> {
    try {
      this.pdfParse = require('pdf-parse')
      console.log('PDFPlugin initialized successfully')
    } catch (error) {
      console.warn('pdf-parse not available, PDFPlugin will not be functional')
      throw error
    }
  }

  canProcess(filePath: string, fileType: string): boolean {
    if (!this.pdfParse) return false
    
    const extension = path.extname(filePath).toLowerCase()
    return fileType === 'pdf' || extension === '.pdf'
  }

  async process(filePath: string): Promise<ProcessedFileContent> {
    if (!this.pdfParse) {
      return {
        error: 'PDF processing not available - pdf-parse module not loaded'
      }
    }

    try {
      const stats = await fs.promises.stat(filePath)
      
      // Check file size (limit to 100MB for PDFs)
      const maxSize = 100 * 1024 * 1024 // 100MB
      if (stats.size > maxSize) {
        return {
          error: `PDF file too large: ${Math.round(stats.size / 1024 / 1024)}MB (max: 100MB)`
        }
      }

      const dataBuffer = await fs.promises.readFile(filePath)
      const data = await this.pdfParse(dataBuffer)

      // Extract additional metadata
      const metadata = {
        pages: data.numpages,
        info: data.info || {},
        version: data.version,
        fileSize: stats.size,
        lastModified: stats.mtime,
        extension: path.extname(filePath),
        processor: this.name,
        
        // Text analysis
        characters: data.text?.length || 0,
        words: data.text ? data.text.split(/\s+/).filter(word => word.length > 0).length : 0,
        lines: data.text ? data.text.split('\n').length : 0,
        
        // PDF-specific metadata
        title: data.info?.Title || '',
        author: data.info?.Author || '',
        subject: data.info?.Subject || '',
        creator: data.info?.Creator || '',
        producer: data.info?.Producer || '',
        creationDate: data.info?.CreationDate || null,
        modificationDate: data.info?.ModDate || null,
        
        // Security info
        encrypted: data.info?.IsAcroFormPresent || false,
        hasForm: data.info?.IsAcroFormPresent || false,
        
        // Processing stats
        processingTime: Date.now()
      }

      return {
        text: data.text || '',
        metadata
      }

    } catch (error: any) {
      console.error('Error processing PDF:', error)
      
      // Provide helpful error messages
      if (error.message?.includes('Invalid PDF')) {
        return {
          error: 'Invalid or corrupted PDF file'
        }
      }
      
      if (error.message?.includes('password')) {
        return {
          error: 'PDF is password protected and cannot be processed'
        }
      }

      return {
        error: `Failed to process PDF: ${error?.message || 'Unknown error'}`
      }
    }
  }

  async cleanup(): Promise<void> {
    this.pdfParse = null
    console.log('PDFPlugin cleaned up')
  }
}
