---
type: "agent_requested"
description: "Meeting Framework Controller"
priority: "system"
context: ["meeting_management", "dialogue_control", "structured_communication"]
authority: "meeting_orchestration"
---

# Structured Dialogue Recording Framework & Agent Workflow

## Framework Overview
This framework enforces consistent persona dialogue format, turn limits, cooldown periods, summary generation, and minute-wise recording for app development meetings.

## Meeting State Management
```
Current Meeting: [INACTIVE]
Meeting Type: [NOT_SET]
Focus Area: [NOT_SET]
Active Participants: []
Turn Counters: {}
Current Minute: 0
Meeting Active: false
```

## Commands Available

### `/start_meeting [meeting_type] [focus_area]`
Initializes a new meeting with specified parameters.

**Example:** `/start_meeting feature_planning context_vault_improvements`

### `/persona_speak [Role] "<message>"`
Allows a persona to contribute to the discussion (max 3 turns per persona).

**Format:** `[Role:] [Opinion/comment including features, modules, files, variables, explanations in natural language]`

### `/generate_brief [project_name] [minute_numbers]`
Creates development brief from specified meeting minutes.

**Example:** `/generate_brief chatlo_vault_update 1,2,3`

### `/owner_message "<message>"`
Forwards owner input via Project Manager proxy.

## Dialogue Rules
1. Each persona can speak **up to 3 rounds** per meeting
2. Messages must follow format: `[Role:] [Content]`
3. No code snippets allowed in dialogue
4. Natural language explanations only
5. After all personas reach 3 turns, meeting enters cooldown
6. Project Manager provides summary after cooldown

## Meeting Minute Format
```
## Minute #[number] [timestamp]

[Role:] message content
[Role:] message content
...
```

## Persona Turn Tracking
- Product Manager: 0/3 turns
- Software Engineer: 0/3 turns  
- DevOps Engineer: 0/3 turns
- QA Engineer: 0/3 turns
- Security Specialist: 0/3 turns

## Meeting Types
- `feature_planning`: New feature development discussions
- `architecture_review`: Technical architecture decisions
- `deployment_planning`: Release and deployment strategies
- `quality_assurance`: Testing and quality discussions
- `security_review`: Security and compliance topics
- `retrospective`: Project review and improvements

## Interaction Protocol
- Owner communicates only via Project Manager
- Project Manager acts as proxy and facilitator
- All technical discussions flow through appropriate personas
- Decisions require consensus or Project Manager arbitration

## Auto-Summary Generation
After cooldown, Project Manager generates:
- Key decisions made
- Responsibilities assigned
- Risks identified and mitigations
- Next steps and action items
- Timeline considerations
