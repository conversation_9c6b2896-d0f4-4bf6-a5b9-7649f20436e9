/**
 * Word Document Processing Plugin
 * Optional plugin for processing Word documents using mammoth
 */

import * as fs from 'fs'
import * as path from 'path'
import { FileProcessorPlugin, ProcessedFileContent } from '../types'

export default class WordPlugin implements FileProcessorPlugin {
  name = 'WordPlugin'
  version = '1.0.0'
  description = 'Optional plugin for processing Word documents'
  author = 'ChatLo Team'
  dependencies = ['mammoth']
  optional = true

  supportedTypes = ['word', 'document']
  supportedExtensions = ['.docx', '.doc']

  private mammoth: any = null

  async initialize(): Promise<void> {
    try {
      this.mammoth = require('mammoth')
      console.log('WordPlugin initialized successfully')
    } catch (error) {
      console.warn('mammoth not available, WordPlugin will not be functional')
      throw error
    }
  }

  canProcess(filePath: string, fileType: string): boolean {
    if (!this.mammoth) return false
    
    const extension = path.extname(filePath).toLowerCase()
    return this.supportedTypes.includes(fileType) || this.supportedExtensions.includes(extension)
  }

  async process(filePath: string): Promise<ProcessedFileContent> {
    if (!this.mammoth) {
      return {
        error: 'Word processing not available - mammoth module not loaded'
      }
    }

    try {
      const stats = await fs.promises.stat(filePath)
      const extension = path.extname(filePath).toLowerCase()
      
      // Check file size (limit to 50MB for Word docs)
      const maxSize = 50 * 1024 * 1024 // 50MB
      if (stats.size > maxSize) {
        return {
          error: `Word document too large: ${Math.round(stats.size / 1024 / 1024)}MB (max: 50MB)`
        }
      }

      // Only process .docx files (mammoth doesn't support .doc)
      if (extension === '.doc') {
        return {
          error: 'Legacy .doc files are not supported. Please convert to .docx format.'
        }
      }

      const buffer = await fs.promises.readFile(filePath)
      const result = await this.mammoth.extractRawText({ buffer })

      const text = result.value || ''
      const messages = result.messages || []

      // Analyze the extracted text
      const analysis = this.analyzeText(text)

      const metadata = {
        fileSize: stats.size,
        lastModified: stats.mtime,
        extension,
        processor: this.name,
        
        // Text analysis
        characters: text.length,
        words: analysis.words,
        lines: analysis.lines,
        paragraphs: analysis.paragraphs,
        
        // Processing info
        extractionMessages: messages.map(msg => ({
          type: msg.type,
          message: msg.message
        })),
        hasWarnings: messages.some(msg => msg.type === 'warning'),
        hasErrors: messages.some(msg => msg.type === 'error'),
        
        // Document structure
        structure: analysis.structure,
        
        processingTime: Date.now()
      }

      return {
        text,
        metadata
      }

    } catch (error: any) {
      console.error('Error processing Word document:', error)
      
      // Provide helpful error messages
      if (error.message?.includes('not a valid zip file')) {
        return {
          error: 'Invalid or corrupted Word document'
        }
      }

      return {
        error: `Failed to process Word document: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Analyze extracted text for structure and content
  private analyzeText(text: string) {
    const lines = text.split('\n').filter(line => line.trim().length > 0)
    const words = text.split(/\s+/).filter(word => word.length > 0)
    
    // Detect paragraphs (double line breaks or significant spacing)
    const paragraphs = text.split(/\n\s*\n/).filter(para => para.trim().length > 0)
    
    // Detect potential headings (lines that are short and followed by content)
    const potentialHeadings = lines.filter(line => {
      const trimmed = line.trim()
      return trimmed.length > 0 && 
             trimmed.length < 100 && 
             !trimmed.endsWith('.') && 
             !trimmed.endsWith(',') &&
             !trimmed.endsWith(';')
    })

    // Detect lists (lines starting with numbers or bullets)
    const listItems = lines.filter(line => {
      const trimmed = line.trim()
      return /^(\d+\.|\*|\-|\•)/.test(trimmed)
    })

    return {
      words: words.length,
      lines: lines.length,
      paragraphs: paragraphs.length,
      structure: {
        potentialHeadings: potentialHeadings.length,
        listItems: listItems.length,
        averageWordsPerParagraph: paragraphs.length > 0 ? Math.round(words.length / paragraphs.length) : 0,
        averageWordsPerLine: lines.length > 0 ? Math.round(words.length / lines.length) : 0
      }
    }
  }

  async cleanup(): Promise<void> {
    this.mammoth = null
    console.log('WordPlugin cleaned up')
  }
}
